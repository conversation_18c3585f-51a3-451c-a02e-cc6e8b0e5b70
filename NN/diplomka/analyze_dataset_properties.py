#!/usr/bin/env python3
"""
Dataset Statistics Analysis Script for SpheroSeg Project
Analyzes image properties and spheroid diameters from masks
Run on server: python3 analyze_dataset_properties.py
"""

import os
import json
import numpy as np
import cv2
from PIL import Image
from pathlib import Path
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

def analyze_dataset(base_path):
    """Analyze complete dataset statistics"""
    
    stats = {
        'training_big': defaultdict(list),
        'training_small': defaultdict(list)
    }
    
    # Dataset paths
    datasets = {
        'training_big': Path(base_path) / 'training_big',
        'training_small': Path(base_path) / 'training_small'
    }
    
    print("="*80)
    print("SPHEROSEG DATASET COMPREHENSIVE ANALYSIS")
    print("="*80)
    
    for dataset_name, dataset_path in datasets.items():
        print(f"\n{'='*40}")
        print(f"Analyzing: {dataset_name}")
        print(f"Path: {dataset_path}")
        print(f"{'='*40}")
        
        # Check if dataset exists
        if not dataset_path.exists():
            print(f"WARNING: Dataset {dataset_name} not found at {dataset_path}")
            continue
            
        # Initialize statistics collectors
        resolutions = []
        formats = set()
        intensities_mean = []
        intensities_std = []
        diameters_pixels = []
        diameters_um = []  # If we have pixel spacing info
        bit_depths = []  # Track bit depths
        
        # Analyze each split
        for split in ['train', 'val', 'test']:
            split_path = dataset_path / split
            if not split_path.exists():
                print(f"  Split '{split}' not found")
                continue
                
            images_path = split_path / 'images'
            masks_path = split_path / 'masks'
            
            if not images_path.exists() or not masks_path.exists():
                print(f"  Split '{split}' missing images or masks directory")
                continue
            
            # Get all image files
            image_files = list(images_path.glob('*'))
            print(f"\n  {split.upper()} split: {len(image_files)} images")
            
            # Process each image
            for idx, img_file in enumerate(image_files):
                if idx % 500 == 0 and idx > 0:
                    print(f"    Processed {idx}/{len(image_files)} images...")
                
                # Analyze image
                try:
                    # Get format
                    formats.add(img_file.suffix.lower())
                    
                    # Open image to get properties
                    img_pil = Image.open(img_file)
                    width, height = img_pil.size
                    resolutions.append((width, height))
                    
                    # Convert to numpy array for intensity analysis
                    img_np = np.array(img_pil)
                    if len(img_np.shape) == 3:
                        # Convert to grayscale if color
                        img_np = cv2.cvtColor(img_np, cv2.COLOR_RGB2GRAY)
                    
                    # Normalize to 0-1 range based on actual bit depth
                    # Detect bit depth based on max value
                    max_val = img_np.max()
                    if max_val > 0:  # Avoid division by zero
                        if max_val <= 255:
                            # 8-bit image
                            img_normalized = img_np.astype(np.float32) / 255.0
                            bit_depths.append(8)
                        elif max_val <= 65535:
                            # 16-bit image
                            img_normalized = img_np.astype(np.float32) / 65535.0
                            bit_depths.append(16)
                        else:
                            # Unknown range, normalize by max value
                            img_normalized = img_np.astype(np.float32) / max_val
                            bit_depths.append(32)  # Assume 32-bit or other
                    else:
                        img_normalized = img_np.astype(np.float32)
                        bit_depths.append(0)  # Empty image
                    
                    intensities_mean.append(np.mean(img_normalized))
                    intensities_std.append(np.std(img_normalized))
                    
                    # Find corresponding mask
                    mask_name = img_file.stem + '.png'  # Masks are usually PNG
                    mask_file = masks_path / mask_name
                    
                    if not mask_file.exists():
                        # Try other extensions
                        for ext in ['.png', '.bmp', '.tiff', '.tif', '.jpg']:
                            mask_file = masks_path / (img_file.stem + ext)
                            if mask_file.exists():
                                break
                    
                    if mask_file.exists():
                        # Load mask
                        mask = cv2.imread(str(mask_file), cv2.IMREAD_GRAYSCALE)
                        
                        if mask is not None:
                            # Find contours (external only)
                            contours, _ = cv2.findContours(
                                mask, 
                                cv2.RETR_EXTERNAL, 
                                cv2.CHAIN_APPROX_SIMPLE
                            )
                            
                            # Calculate diameter for each contour
                            for contour in contours:
                                # Calculate area and equivalent diameter
                                area = cv2.contourArea(contour)
                                if area > 100:  # Filter out tiny noise
                                    # Equivalent diameter (diameter of circle with same area)
                                    diameter_px = 2 * np.sqrt(area / np.pi)
                                    diameters_pixels.append(diameter_px)
                                    
                                    # Also calculate using bounding circle
                                    (x, y), radius = cv2.minEnclosingCircle(contour)
                                    # diameters_pixels.append(radius * 2)  # Alternative method
                    
                except Exception as e:
                    print(f"    Error processing {img_file.name}: {str(e)}")
        
        # Calculate statistics
        print(f"\n  {'='*35}")
        print(f"  RESULTS FOR {dataset_name.upper()}")
        print(f"  {'='*35}")
        
        # Resolution statistics
        if resolutions:
            res_array = np.array(resolutions)
            widths = res_array[:, 0]
            heights = res_array[:, 1]
            
            print(f"\n  IMAGE RESOLUTIONS:")
            print(f"    Total images analyzed: {len(resolutions)}")
            print(f"    Unique resolutions: {len(set(resolutions))}")
            print(f"    Width  - Min: {np.min(widths):4.0f}, Max: {np.max(widths):4.0f}, "
                  f"Mean: {np.mean(widths):6.1f}, Median: {np.median(widths):4.0f}, "
                  f"Std: {np.std(widths):5.1f}")
            print(f"    Height - Min: {np.min(heights):4.0f}, Max: {np.max(heights):4.0f}, "
                  f"Mean: {np.mean(heights):6.1f}, Median: {np.median(heights):4.0f}, "
                  f"Std: {np.std(heights):5.1f}")
            
            # Show most common resolutions
            from collections import Counter
            res_counter = Counter(resolutions)
            print(f"\n    Most common resolutions:")
            for res, count in res_counter.most_common(5):
                print(f"      {res[0]}x{res[1]}: {count} images ({100*count/len(resolutions):.1f}%)")
        
        # Format statistics
        print(f"\n  IMAGE FORMATS:")
        print(f"    Formats found: {', '.join(sorted(formats))}")
        
        # Bit depth statistics
        if bit_depths:
            from collections import Counter
            bit_depth_counts = Counter(bit_depths)
            print(f"\n  BIT DEPTHS:")
            for depth, count in sorted(bit_depth_counts.items()):
                if depth > 0:
                    print(f"    {depth}-bit: {count} images ({100*count/len(bit_depths):.1f}%)")
        
        # Intensity statistics
        if intensities_mean:
            print(f"\n  IMAGE INTENSITIES (normalized to 0-1 range):")
            print(f"    Mean intensity per image:")
            print(f"      Min: {np.min(intensities_mean):.4f}, Max: {np.max(intensities_mean):.4f}, "
                  f"Mean: {np.mean(intensities_mean):.4f}, Median: {np.median(intensities_mean):.4f}, "
                  f"Std: {np.std(intensities_mean):.4f}")
            print(f"    Std intensity per image:")
            print(f"      Min: {np.min(intensities_std):.4f}, Max: {np.max(intensities_std):.4f}, "
                  f"Mean: {np.mean(intensities_std):.4f}, Median: {np.median(intensities_std):.4f}, "
                  f"Std: {np.std(intensities_std):.4f}")
            print(f"    Note: Images normalized based on detected bit depth (8-bit: /255, 16-bit: /65535)")
        
        # Spheroid diameter statistics
        if diameters_pixels:
            print(f"\n  SPHEROID DIAMETERS (in pixels):")
            print(f"    Total spheroids measured: {len(diameters_pixels)}")
            print(f"    Min: {np.min(diameters_pixels):6.1f} px")
            print(f"    Max: {np.max(diameters_pixels):6.1f} px")
            print(f"    Mean: {np.mean(diameters_pixels):6.1f} px")
            print(f"    Median: {np.median(diameters_pixels):6.1f} px")
            print(f"    Std: {np.std(diameters_pixels):6.1f} px")
            print(f"    25th percentile: {np.percentile(diameters_pixels, 25):6.1f} px")
            print(f"    75th percentile: {np.percentile(diameters_pixels, 75):6.1f} px")
            
            # If we assume a pixel-to-micron conversion (this is a rough estimate)
            # Typical microscopy might be ~1-2 microns per pixel
            # But this needs to be verified from actual microscope settings
            print(f"\n    Note: To convert to micrometers, actual pixel spacing from")
            print(f"          microscope metadata is needed. Common ranges:")
            print(f"          - At 4x magnification: ~2.5 µm/pixel")
            print(f"          - At 10x magnification: ~1.0 µm/pixel")
            
            # Provide estimates for both magnifications
            for mag, pixel_size in [(4, 2.5), (10, 1.0)]:
                diam_um = np.array(diameters_pixels) * pixel_size
                print(f"\n    Estimated at {mag}x magnification ({pixel_size} µm/px):")
                print(f"      Range: {np.min(diam_um):.0f} - {np.max(diam_um):.0f} µm")
                print(f"      Mean: {np.mean(diam_um):.0f} µm, Median: {np.median(diam_um):.0f} µm")
        
        # Store results
        stats[dataset_name] = {
            'n_images': len(resolutions),
            'resolutions': resolutions,
            'formats': list(formats),
            'bit_depths': bit_depths,
            'intensities_mean': intensities_mean,
            'intensities_std': intensities_std,
            'diameters_pixels': diameters_pixels
        }
    
    # Save detailed results to JSON
    print("\n" + "="*80)
    print("SAVING RESULTS...")
    print("="*80)
    
    # Prepare JSON-serializable summary
    summary = {}
    for dataset_name, data in stats.items():
        if data.get('resolutions'):
            res_array = np.array(data['resolutions'])
            summary[dataset_name] = {
                'n_images': data['n_images'],
                'formats': data['formats'],
                'resolution_stats': {
                    'min_width': int(np.min(res_array[:, 0])),
                    'max_width': int(np.max(res_array[:, 0])),
                    'mean_width': float(np.mean(res_array[:, 0])),
                    'min_height': int(np.min(res_array[:, 1])),
                    'max_height': int(np.max(res_array[:, 1])),
                    'mean_height': float(np.mean(res_array[:, 1])),
                    'unique_resolutions': len(set(data['resolutions']))
                },
                'intensity_stats': {
                    'mean_of_means': float(np.mean(data['intensities_mean'])) if data['intensities_mean'] else None,
                    'std_of_means': float(np.std(data['intensities_mean'])) if data['intensities_mean'] else None,
                },
                'diameter_stats_pixels': {
                    'n_spheroids': len(data['diameters_pixels']),
                    'min': float(np.min(data['diameters_pixels'])) if data['diameters_pixels'] else None,
                    'max': float(np.max(data['diameters_pixels'])) if data['diameters_pixels'] else None,
                    'mean': float(np.mean(data['diameters_pixels'])) if data['diameters_pixels'] else None,
                    'median': float(np.median(data['diameters_pixels'])) if data['diameters_pixels'] else None,
                    'std': float(np.std(data['diameters_pixels'])) if data['diameters_pixels'] else None,
                }
            }
    
    # Save JSON summary
    output_file = 'dataset_analysis_results.json'
    with open(output_file, 'w') as f:
        json.dump(summary, f, indent=2)
    print(f"Results saved to: {output_file}")
    
    # Also save detailed CSV for further analysis
    for dataset_name, data in stats.items():
        if data.get('diameters_pixels'):
            csv_file = f'{dataset_name}_diameters.csv'
            np.savetxt(csv_file, data['diameters_pixels'], delimiter=',', 
                      header='diameter_pixels', comments='')
            print(f"Diameters saved to: {csv_file}")
    
    print("\n" + "="*80)
    print("ANALYSIS COMPLETE!")
    print("="*80)

if __name__ == "__main__":
    # Run analysis on the server path
    base_path = "/data/prusek"
    analyze_dataset(base_path)